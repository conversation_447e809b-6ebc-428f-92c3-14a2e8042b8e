/**
 * InterviewAvatar component - Displays a realistic human avatar for the interview
 */
import { useEffect, useRef, useState } from "react";
import { useLiveAPIContext } from "../../contexts/LiveAPIContext";
import TranscriptionDisplay from "../transcription-display/TranscriptionDisplay";
import "./interview-avatar.scss";
import humanAvatarSvg from "../../assets/human-avatar.svg";

export default function InterviewAvatar() {
  const { volume, connected, client } = useLiveAPIContext();
  const avatarRef = useRef<HTMLObjectElement>(null);
  const [speaking, setSpeaking] = useState(false);
  const [currentText, setCurrentText] = useState<string>("");

  // Detect when the AI is speaking based on volume
  useEffect(() => {
    if (volume > 0.05) {
      setSpeaking(true);
    } else {
      setSpeaking(false);
    }
  }, [volume]);

  // Add a direct listener for model text as a fallback
  useEffect(() => {
    console.log("Setting up direct modeltext listener in InterviewAvatar");

    const handleModelText = (text: string) => {
      console.log("InterviewAvatar received model text:", text);
      setCurrentText(text);
    };

    const handleTurnComplete = () => {
      // Clear text after a delay
      setTimeout(() => {
        setCurrentText("");
      }, 2000);
    };

    client.on("modeltext", handleModelText);
    client.on("turncomplete", handleTurnComplete);

    return () => {
      client.off("modeltext", handleModelText);
      client.off("turncomplete", handleTurnComplete);
    };
  }, [client]);

  // Function to update the mouth based on speaking state
  const updateMouth = () => {
    if (avatarRef.current && avatarRef.current.contentDocument) {
      const svgDoc = avatarRef.current.contentDocument;
      const mouthClosed = svgDoc.getElementById('mouth-closed');
      const mouthOpen = svgDoc.getElementById('mouth-open');

      if (mouthClosed && mouthOpen) {
        if (speaking) {
          mouthClosed.style.display = 'none';
          mouthOpen.style.display = 'block';
        } else {
          mouthClosed.style.display = 'block';
          mouthOpen.style.display = 'none';
        }
      }
    }
  };

  // Handle SVG load event
  useEffect(() => {
    const handleLoad = () => {
      console.log("SVG loaded, setting up mouth animation");
      updateMouth();
    };

    const avatarElement = avatarRef.current;
    if (avatarElement) {
      avatarElement.addEventListener('load', handleLoad);
      return () => {
        avatarElement.removeEventListener('load', handleLoad);
      };
    }
  }, [avatarRef]);

  // Update mouth when speaking state changes
  useEffect(() => {
    updateMouth();
  }, [speaking]);

  return (
    <div className="interview-avatar-container">
      {/* Speech transcription display */}
      <TranscriptionDisplay />

      <div className={`interview-avatar-wrapper ${speaking ? 'speaking' : ''} ${connected ? 'active' : ''}`}>
        {/* Human avatar using SVG */}
        <object
          ref={avatarRef}
          type="image/svg+xml"
          data={humanAvatarSvg}
          className="human-avatar"
          aria-label="Human avatar"
        />
      </div>
      <div className="avatar-name">Interview Assistant</div>
    </div>
  );
}
