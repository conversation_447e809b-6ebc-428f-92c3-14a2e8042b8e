/**
 * Mock Interview Application
 */

import { useRef, useState, useEffect } from "react";
import "./App.scss";
import { LiveAPIProvider } from "./contexts/LiveAPIContext";
import ControlTray from "./components/control-tray/ControlTray";
import cn from "classnames";
import GetStarted from "./components/get-started/GetStarted";
import InterviewAvatar from "./components/interview-avatar/InterviewAvatar";
import InterviewQuestions from "./components/interview-questions/InterviewQuestions";

const API_KEY = process.env.REACT_APP_GEMINI_API_KEY as string;
if (typeof API_KEY !== "string") {
  throw new Error("set REACT_APP_GEMINI_API_KEY in .env");
}

const host = "generativelanguage.googleapis.com";
const uri = `wss://${host}/ws/google.ai.generativelanguage.v1alpha.GenerativeService.BidiGenerateContent`;

function App() {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [videoStream, setVideoStream] = useState<MediaStream | null>(null);
  const [interviewStarted, setInterviewStarted] = useState(false);
  const [modelText, setModelText] = useState<string>("Waiting for model text...");

  const handleStartInterview = () => {
    setInterviewStarted(true);
  };

  // Direct text display in the App component using a global event listener
  useEffect(() => {
    const handleModelText = (event: CustomEvent) => {
      const text = event.detail;
      console.log("App received model text from custom event:", text);
      setModelText(`Model said: ${text}`);
    };

    // Use a custom event to receive model text
    window.addEventListener('modelTextEvent' as any, handleModelText as any);

    return () => {
      window.removeEventListener('modelTextEvent' as any, handleModelText as any);
    };
  }, []);

  return (
    <div className="App">
      <LiveAPIProvider url={uri} apiKey={API_KEY}>
        <div className="interview-app">
          {!interviewStarted ? (
            <GetStarted onStart={handleStartInterview} />
          ) : (
            <main className="interview-main">
              <div className="interview-header">
                <h1>Mock Interview Session</h1>
                {/* Direct text display in the header */}
                <div className="model-text-display">
                  <p>{modelText}</p>
                </div>
              </div>

              <div className="interview-content">
                <InterviewAvatar />

                <video
                  className={cn("user-video", {
                    hidden: !videoRef.current || !videoStream,
                  })}
                  ref={videoRef}
                  autoPlay
                  playsInline
                  muted
                />

                <InterviewQuestions />
              </div>

              <div className="interview-controls">
                <ControlTray
                  videoRef={videoRef}
                  supportsVideo={true}
                  onVideoStreamChange={setVideoStream}
                  enableEditingSettings={false}
                />
              </div>
            </main>
          )}
        </div>
      </LiveAPIProvider>
    </div>
  );
}

export default App;
