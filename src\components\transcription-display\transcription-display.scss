.transcription-container {
  position: absolute;
  top: -80px; /* Position above the avatar */
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  max-width: 400px;
  margin: 0 auto;
  background-color: white;
  border: 2px solid var(--primary-orange);
  border-radius: 20px;
  padding: 10px 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.3s ease;

  &.visible {
    opacity: 1;
    transform: translateX(-50%);
  }

  /* Add speech bubble tail */
  &::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    border-width: 15px 15px 0;
    border-style: solid;
    border-color: white transparent transparent;
  }

  &::before {
    content: '';
    position: absolute;
    bottom: -18px;
    left: 50%;
    transform: translateX(-50%);
    border-width: 17px 17px 0;
    border-style: solid;
    border-color: var(--primary-orange) transparent transparent;
    z-index: -1;
  }

  .transcription-content {
    p {
      margin: 0;
      font-size: 16px;
      line-height: 1.5;
      color: var(--text-dark);
      text-align: center;
      font-family: var(--font-family);
      font-weight: 500;
    }
  }
}
